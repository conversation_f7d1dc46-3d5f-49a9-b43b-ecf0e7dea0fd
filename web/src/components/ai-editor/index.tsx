// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

"use client";

import { useState, useCallback } from "react";
import {
  EditorRoot,
  EditorContent,
  type EditorInstance,
  type JSONContent,
} from "novel";
import type { Content } from "@tiptap/react";
import { useDebouncedCallback } from "use-debounce";

// 我们自己的扩展配置
import { aiEditorExtensions } from "./extensions";
import { AIToolbar } from "./ai-toolbar";
import { AIAssistant } from "./ai-assistant";

// 样式
import "./styles.css";

export interface AIEditorProps {
  initialContent?: Content;
  onContentChange?: (content: JSONContent) => void;
  onMarkdownChange?: (markdown: string) => void;
  placeholder?: string;
  className?: string;
}

export function AIEditor({
  initialContent,
  onContentChange,
  onMarkdownChange,
  placeholder = "开始写作，输入 '/' 查看 AI 命令...",
  className = "",
}: AIEditorProps) {
  // 编辑器状态
  const [editor, setEditor] = useState<EditorInstance | null>(null);
  const [isAIOpen, setIsAIOpen] = useState(false);
  const [selectedText, setSelectedText] = useState("");
  const [aiSuggestion, setAISuggestion] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  // 防抖更新函数
  const debouncedUpdate = useDebouncedCallback(
    (editor: EditorInstance) => {
      // 获取 JSON 内容
      const jsonContent = editor.getJSON();
      onContentChange?.(jsonContent);

      // 获取 Markdown 内容
      if (onMarkdownChange && editor.storage.markdown) {
        const markdown = editor.storage.markdown.getMarkdown();
        onMarkdownChange(markdown);
      }
    },
    300
  );

  // AI 文本生成函数
  const generateAIText = useCallback(async (prompt: string, context?: string) => {
    setIsLoading(true);
    try {
      // 模拟 AI API 调用
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // 模拟 AI 响应
      const responses = [
        "这是一个很有趣的想法。让我们深入探讨一下这个概念的各个方面。",
        "基于你的描述，我建议从以下几个角度来分析这个问题：",
        "这个主题确实值得深入研究。我们可以从历史背景开始，然后分析现状。",
        "你提到的观点很有见地。让我为你扩展一些相关的思考。",
      ];
      
      const randomResponse = responses[Math.floor(Math.random() * responses.length)];
      setAISuggestion(randomResponse);
      
    } catch (error) {
      console.error("AI 生成失败:", error);
      setAISuggestion("抱歉，AI 生成失败，请稍后重试。");
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 处理文本选择
  const handleTextSelection = useCallback(() => {
    if (!editor) return;

    const { from, to } = editor.state.selection;
    if (from === to) {
      setSelectedText("");
      setIsAIOpen(false);
      return;
    }

    const text = editor.state.doc.textBetween(from, to);
    setSelectedText(text);
    setIsAIOpen(true);
  }, [editor]);

  // 插入 AI 生成的文本
  const insertAIText = useCallback((text: string) => {
    if (!editor) return;

    editor.chain().focus().insertContent(text).run();
    setAISuggestion("");
    setIsAIOpen(false);
  }, [editor]);

  // 替换选中的文本
  const replaceSelectedText = useCallback((text: string) => {
    if (!editor) return;

    const { from, to } = editor.state.selection;
    editor.chain().focus().deleteRange({ from, to }).insertContent(text).run();
    setAISuggestion("");
    setIsAIOpen(false);
  }, [editor]);

  return (
    <div className={`ai-editor-container ${className}`}>
      <EditorRoot>
        <EditorContent
          immediatelyRender={false}
          initialContent={initialContent}
          extensions={aiEditorExtensions}
          className="ai-editor-content"
          editorProps={{
            attributes: {
              class: "ai-editor-prose",
              "data-placeholder": placeholder,
            },
          }}
          onCreate={({ editor }) => {
            setEditor(editor);
          }}
          onUpdate={({ editor }) => {
            debouncedUpdate(editor);
          }}
          onSelectionUpdate={handleTextSelection}
        >
          {/* AI 工具栏 */}
          <AIToolbar
            editor={editor}
            onAIGenerate={generateAIText}
            isLoading={isLoading}
          />
        </EditorContent>
      </EditorRoot>

      {/* AI 助手面板 */}
      {isAIOpen && (
        <AIAssistant
          selectedText={selectedText}
          suggestion={aiSuggestion}
          isLoading={isLoading}
          onGenerate={generateAIText}
          onInsert={insertAIText}
          onReplace={replaceSelectedText}
          onClose={() => setIsAIOpen(false)}
        />
      )}
    </div>
  );
}

export default AIEditor;
