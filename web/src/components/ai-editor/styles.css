/* AI Editor 样式 */

.ai-editor-container {
  @apply relative w-full min-h-[400px] border border-border rounded-lg overflow-hidden;
}

.ai-editor-content {
  @apply w-full h-full;
}

.ai-editor-prose {
  @apply prose prose-base dark:prose-invert max-w-none p-4 focus:outline-none;
  @apply prose-headings:font-semibold prose-headings:text-foreground;
  @apply prose-p:text-muted-foreground prose-p:leading-relaxed;
  @apply prose-a:text-primary prose-a:no-underline hover:prose-a:underline;
  @apply prose-strong:text-foreground;
  @apply prose-code:text-primary prose-code:bg-muted;
  @apply prose-pre:bg-muted;
  min-height: 400px;
}

/* 占位符样式 */
.ai-editor-prose:empty::before {
  content: attr(data-placeholder);
  @apply text-muted-foreground pointer-events-none float-left h-0;
}

/* 列表样式 */
.ai-editor-bullet-list {
  @apply list-disc list-outside ml-6 space-y-1;
}

.ai-editor-ordered-list {
  @apply list-decimal list-outside ml-6 space-y-1;
}

/* 引用样式 */
.ai-editor-blockquote {
  @apply border-l-4 border-primary pl-4 italic text-muted-foreground bg-muted py-2 my-4;
}

/* 代码样式 */
.ai-editor-code {
  @apply bg-muted text-primary px-1 py-0.5 rounded text-sm;
}

/* 标题样式 */
.ai-editor-heading {
  @apply font-semibold text-foreground mt-6 mb-3;
}

.ai-editor-heading[data-level="1"] {
  @apply text-2xl;
}

.ai-editor-heading[data-level="2"] {
  @apply text-xl;
}

.ai-editor-heading[data-level="3"] {
  @apply text-lg;
}

/* 链接样式 */
.ai-editor-link {
  @apply text-primary hover:text-primary/80 transition-colors cursor-pointer;
}

/* 工具栏气泡样式 */
.ai-toolbar-bubble {
  @apply z-50;
}

/* 选择高亮样式 */
.ProseMirror-selectednode {
  @apply outline-none bg-accent rounded;
}

/* AI 高亮样式 */
.ai-editor-prose mark[data-color="#3b82f6"] {
  @apply bg-accent text-accent-foreground px-1 rounded;
}

/* 焦点样式 */
.ai-editor-content .ProseMirror:focus {
  @apply outline-none;
}

/* 拖拽样式 */
.ai-editor-prose .ProseMirror-drop-cursor {
  @apply border-l-2 border-primary;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .ai-editor-prose {
    @apply p-3 text-sm;
  }
  
  .ai-toolbar-bubble {
    @apply scale-90;
  }
}

/* 动画效果 */
.ai-editor-container {
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态 */
.ai-loading {
  @apply opacity-50 pointer-events-none;
}

/* 错误状态 */
.ai-error {
  @apply border-destructive bg-destructive/10;
}

/* 成功状态 */
.ai-success {
  @apply border-green-500 bg-green-500/10;
}
